package com.example.uvccam.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidViewBinding
import com.example.uvccam.databinding.CameraHostLayoutBinding // 替换为您的包名

@Composable
fun UvcCameraScreen(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.statusBars)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "UVC Camera with Jetpack Compose",
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 使用AndroidViewBinding来加载XML并嵌入Fragment
        // 使用weight(1f)让相机预览占据剩余空间，避免挤压标题
        Box(modifier = Modifier
            .fillMaxSize()
            .weight(1f)
        ) {
            AndroidViewBinding(
                factory = CameraHostLayoutBinding::inflate,
                modifier = Modifier.fillMaxSize()
            ) {
                // 'this' 是 CameraHostLayoutBinding 的一个实例
                // 在这里我们不需要做任何额外操作，因为
                // FragmentContainerView 会自动处理Fragment的加载和生命周期
            }
        }
    }
}