package com.example.uvccam

import android.content.Context
import android.content.res.Configuration
import android.hardware.usb.UsbDevice
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.jiangdg.ausbc.MultiCameraClient
import com.jiangdg.ausbc.base.CameraFragment
import com.jiangdg.ausbc.callback.ICameraStateCallBack
import com.jiangdg.ausbc.camera.CameraUVC
import com.jiangdg.ausbc.camera.bean.CameraRequest
import com.jiangdg.ausbc.render.env.RotateType
import com.jiangdg.ausbc.widget.AspectRatioTextureView
import com.jiangdg.ausbc.widget.IAspectRatio
import com.example.uvccam.databinding.FragmentUvcCameraBinding

class UvcCameraFragment : CameraFragment() {
    private var _binding: FragmentUvcCameraBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUvcCameraBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // The parent CameraFragment will handle camera initialization
    }

    /**
     * Allow runtime adjustment of camera rotation if needed
     * Call this method to change rotation without restarting camera
     */
    fun adjustCameraRotation(rotateType: RotateType) {
        setRotateType(rotateType)
    }

    override fun getRootView(inflater: LayoutInflater, container: ViewGroup?): View {
        return onCreateView(inflater, container, null)
    }

    override fun getCameraView(): IAspectRatio? {
        return binding.tvCameraRender
    }

    override fun getCameraViewContainer(): ViewGroup? {
        return binding.container
    }

    override fun generateCamera(ctx: Context, device: UsbDevice): MultiCameraClient.ICamera {
        return CameraUVC(ctx, device)
    }

    override fun getCameraRequest(): CameraRequest {
        // Get optimal resolution based on device screen
        val (previewWidth, previewHeight) = getOptimalPreviewSize()

        return CameraRequest.Builder()
            // Dynamic resolution based on device screen size
            .setPreviewWidth(previewWidth)
            .setPreviewHeight(previewHeight)
            .setRenderMode(CameraRequest.RenderMode.OPENGL)
            // Set 90-degree rotation to correct orientation issue
            .setDefaultRotateType(RotateType.ANGLE_90)
            .setAudioSource(CameraRequest.AudioSource.SOURCE_SYS_MIC)
            .setPreviewFormat(CameraRequest.PreviewFormat.FORMAT_MJPEG)
            .setAspectRatioShow(true)
            .setCaptureRawImage(false)
            .setRawPreviewData(false)
            .create()
    }

    /**
     * Get optimal preview size based on device screen resolution
     * Optimized for Xiaomi 15 (2670 x 1200) and other high-resolution devices
     */
    private fun getOptimalPreviewSize(): Pair<Int, Int> {
        val displayMetrics = DisplayMetrics()
        @Suppress("DEPRECATION")
        requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)

        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        val screenAspectRatio = screenWidth.toFloat() / screenHeight.toFloat()

        // For Xiaomi 15 (2670 x 1200) and similar high-resolution devices
        return when {
            // High resolution devices (like Xiaomi 15)
            screenWidth >= 2400 -> {
                if (screenAspectRatio > 2.0f) {
                    // Ultra-wide screens (like 2670x1200)
                    Pair(1920, 1080) // Full HD for better performance
                } else {
                    // Standard high-res screens
                    Pair(1280, 720) // HD for good balance
                }
            }
            // Medium resolution devices
            screenWidth >= 1800 -> Pair(1280, 720)
            // Lower resolution devices
            else -> Pair(640, 480)
        }
    }

    override fun onCameraState(
        self: MultiCameraClient.ICamera,
        code: ICameraStateCallBack.State,
        msg: String?
    ) {
        // Check if fragment is attached to avoid crashes
        if (!isAdded || context == null) {
            return
        }

        try {
            when (code) {
                ICameraStateCallBack.State.OPENED -> {
                    Toast.makeText(requireContext(), "摄像头已开启", Toast.LENGTH_SHORT).show()
                }
                ICameraStateCallBack.State.CLOSED -> {
                    Toast.makeText(requireContext(), "摄像头已关闭", Toast.LENGTH_SHORT).show()
                }
                ICameraStateCallBack.State.ERROR -> {
                    Toast.makeText(requireContext(), "错误: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: IllegalStateException) {
            // Fragment not attached to context - ignore the callback
            android.util.Log.w("UvcCameraFragment", "Fragment not attached when handling camera state: $code")
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}